# utils.py
import sys
import os
import subprocess
from selenium import webdriver
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.firefox.options import Options as FirefoxOptions
from selenium.common.exceptions import WebDriverException, SessionNotCreatedException
from webdriver_manager.chrome import ChromeDriverManager
from webdriver_manager.firefox import GeckoDriverManager


def check_browser_installed(browser_name):
    """
    Check if a browser is installed on the system.

    Args:
        browser_name (str): Name of the browser ('chrome' or 'firefox')

    Returns:
        bool: True if browser is installed, False otherwise
    """
    try:
        if browser_name.lower() == 'chrome':
            # Check for Chrome/Chromium on different systems
            chrome_commands = ['google-chrome', 'chromium-browser', 'chromium', 'chrome']
            for cmd in chrome_commands:
                try:
                    subprocess.run([cmd, '--version'],
                                 capture_output=True, check=True, timeout=5)
                    print(f"✓ Found Chrome/Chromium: {cmd}")
                    return True
                except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
                    continue
        elif browser_name.lower() == 'firefox':
            # Check for Firefox
            firefox_commands = ['firefox', 'firefox-esr']
            for cmd in firefox_commands:
                try:
                    subprocess.run([cmd, '--version'],
                                 capture_output=True, check=True, timeout=5)
                    print(f"✓ Found Firefox: {cmd}")
                    return True
                except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
                    continue
        return False
    except Exception as e:
        print(f"Error checking {browser_name}: {e}")
        return False


def create_chrome_driver():
    """
    Create and return a Chrome WebDriver instance with optimized options.

    Returns:
        webdriver.Chrome: Chrome WebDriver instance

    Raises:
        Exception: If Chrome driver creation fails
    """
    try:
        print("🚀 Launching Chrome...")

        # Configure Chrome options for better performance and compatibility
        chrome_options = ChromeOptions()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # Create Chrome driver
        driver = webdriver.Chrome(
            service=webdriver.chrome.service.Service(ChromeDriverManager().install()),
            options=chrome_options
        )

        # Execute script to remove webdriver property
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        print("✅ Chrome launched successfully!")
        return driver

    except Exception as e:
        print(f"❌ Chrome launch failed: {e}")
        raise


def create_firefox_driver():
    """
    Create and return a Firefox WebDriver instance with optimized options.

    Returns:
        webdriver.Firefox: Firefox WebDriver instance

    Raises:
        Exception: If Firefox driver creation fails
    """
    try:
        print("🚀 Launching Firefox...")

        # Configure Firefox options
        firefox_options = FirefoxOptions()
        firefox_options.set_preference("dom.webdriver.enabled", False)
        firefox_options.set_preference('useAutomationExtension', False)

        # Create Firefox driver
        driver = webdriver.Firefox(
            service=webdriver.firefox.service.Service(GeckoDriverManager().install()),
            options=firefox_options
        )

        print("✅ Firefox launched successfully!")
        return driver

    except Exception as e:
        print(f"❌ Firefox launch failed: {e}")
        raise


def get_browser_driver():
    """
    Get a browser driver with intelligent fallback logic.
    Priority: Chrome -> Firefox -> Error

    Returns:
        webdriver: WebDriver instance (Chrome or Firefox)

    Raises:
        SystemExit: If no browsers are available
    """
    print("🔍 Detecting available browsers...")

    # Check which browsers are installed
    chrome_available = check_browser_installed('chrome')
    firefox_available = check_browser_installed('firefox')

    # If no browsers are available, show error and exit
    if not chrome_available and not firefox_available:
        print("\n❌ ERROR: No supported browsers found!")
        print("Please install one of the following:")
        print("  • Google Chrome: https://www.google.com/chrome/")
        print("  • Mozilla Firefox: https://www.mozilla.org/firefox/")
        print("\nExiting application...")
        sys.exit(1)

    # Try Chrome first (preferred)
    if chrome_available:
        try:
            return create_chrome_driver()
        except Exception as e:
            print(f"⚠️  Chrome failed to start: {e}")
            if firefox_available:
                print("🔄 Falling back to Firefox...")
            else:
                print("❌ No fallback browser available!")
                sys.exit(1)

    # Try Firefox as fallback
    if firefox_available:
        try:
            return create_firefox_driver()
        except Exception as e:
            print(f"❌ Firefox also failed: {e}")
            print("❌ All browsers failed to start!")
            print("Please check your browser installations and try again.")
            sys.exit(1)

    # This should never be reached due to earlier checks
    print("❌ Unexpected error: No browser driver could be created!")
    sys.exit(1)
