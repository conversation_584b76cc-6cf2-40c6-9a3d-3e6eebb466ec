# utils.py
import sys
from selenium import webdriver
from selenium.common.exceptions import WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from webdriver_manager.firefox import GeckoDriverManager


def get_browser_driver():
    browser = "chrome"
    if len(sys.argv) > 1:
        browser = sys.argv[1].lower()

    try:
        if browser == "firefox":
            print("Launching Firefox...")
            return webdriver.Firefox(executable_path=GeckoDriverManager().install())
        else:
            print("Launching Chrome...")
            return webdriver.Chrome(ChromeDriverManager().install())
    except WebDriverException:
        print(f"{browser.capitalize()} not found or failed. Trying fallback...")

        # Try fallback
        try:
            if browser == "chrome":
                return webdriver.Firefox(executable_path=GeckoDriverManager().install())
            else:
                return webdriver.Chrome(ChromeDriverManager().install())
        except Exception as e:
            print("Both browsers failed:", e)
            raise
