
# config.py - LinkedIn Auto-Apply Configuration

# List of job search queries to iterate through
JOB_SEARCH_QUERIES = [
    "Web Developer Fresher",
    "Frontend Developer Entry Level",
    "Backend Developer Junior",
    "Full Stack Developer Fresher",
    "Software Engineer Entry Level",
    "Python Developer Fresher",
    "JavaScript Developer Junior",
    "React Developer Entry Level",
    "Node.js Developer Fresher",
    "Software Developer Intern"
]

# Timing configuration (in seconds)
TIMING_CONFIG = {
    "page_load_wait": 3,        # Time to wait for pages to load
    "element_wait": 5,          # Time to wait for elements to appear
    "search_wait": 5,           # Time to wait after performing search
    "login_check_wait": 3,      # Time to wait when checking login status
    "between_queries_wait": 2,  # Time to wait between different search queries
}

# LinkedIn URLs
LINKEDIN_URLS = {
    "home": "https://www.linkedin.com",
    "login": "https://www.linkedin.com/login",
    "jobs": "https://www.linkedin.com/jobs/",
    "feed": "https://www.linkedin.com/feed/"
}

# CSS Selectors for LinkedIn elements
LINKEDIN_SELECTORS = {
    "job_search_box": "input.jobs-search-box__text-input",
    "login_email": "#username",
    "login_password": "#password",
    "login_button": "button[type='submit']",
    "profile_menu": "button[aria-label*='View profile']",
    "jobs_link": "a[href*='/jobs/']"
}

# Application settings
APP_CONFIG = {
    "max_retries": 3,           # Maximum retries for failed operations
    "enable_debug_mode": False, # Enable debug logging
    "auto_close_browser": False # Whether to auto-close browser on completion
}
