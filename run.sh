#!/bin/bash
# run.sh - LinkedIn Auto-Apply <PERSON>t Runner Script

echo "🚀 LinkedIn Auto-Apply Bot Runner"
echo "=================================="

# Check if virtual environment exists
if [ -d "venv" ]; then
    echo "📦 Activating virtual environment..."
    source venv/bin/activate
else
    echo "⚠️  No virtual environment found. Using system Python."
    echo "💡 Consider creating one with: python3 -m venv venv"
fi

# Check if dependencies are installed
echo "🔍 Checking dependencies..."
python -c "import selenium" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ Selenium not found. Installing dependencies..."
    pip install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install dependencies. Please check your pip installation."
        exit 1
    fi
else
    echo "✅ Dependencies are installed"
fi

# Run tests first
echo "🧪 Running quick tests..."
python test_app.py
if [ $? -ne 0 ]; then
    echo "❌ Tests failed. Please fix the issues before running the application."
    exit 1
fi

echo ""
echo "🎯 Starting LinkedIn Auto-Apply Bot..."
echo "=================================="

# Run the main application
python main.py

echo ""
echo "👋 Thanks for using LinkedIn Auto-Apply Bot!"
