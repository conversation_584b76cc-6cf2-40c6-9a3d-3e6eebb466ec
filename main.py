# main.py - LinkedIn Auto-Apply Application Entry Point

import sys
import time
from utils import get_browser_driver
from linkedin_bot import linkedin_automation_workflow
from config import APP_CONFIG


def print_welcome_message():
    """Print welcome message and application information."""
    print("="*60)
    print("🚀 LINKEDIN AUTO-APPLY BOT")
    print("="*60)
    print("This application will help you search for jobs on LinkedIn")
    print("using predefined search queries from the configuration.")
    print()
    print("Features:")
    print("• ✅ Automatic browser detection (Chrome → Firefox)")
    print("• ✅ Smart login detection")
    print("• ✅ Multiple job search queries")
    print("• ✅ Comprehensive error handling")
    print("• ✅ Detailed progress reporting")
    print()
    print("⚠️  IMPORTANT NOTES:")
    print("• You may need to log in manually when prompted")
    print("• The browser window will open automatically")
    print("• Please don't close the browser window manually")
    print("="*60)
    print()


def print_completion_message(success):
    """
    Print completion message based on success status.

    Args:
        success (bool): Whether the workflow completed successfully
    """
    print("\n" + "="*60)
    if success:
        print("🎉 APPLICATION COMPLETED SUCCESSFULLY!")
        print("✅ Job searches have been performed")
        print("✅ Check the results summary above")
    else:
        print("⚠️  APPLICATION COMPLETED WITH ISSUES")
        print("❌ Some operations may have failed")
        print("💡 Check the error messages above for details")

    print("="*60)


def handle_browser_cleanup(driver):
    """
    Handle browser cleanup with user interaction.

    Args:
        driver: WebDriver instance to clean up
    """
    try:
        if APP_CONFIG.get("auto_close_browser", False):
            print("🔄 Auto-closing browser...")
            time.sleep(3)
        else:
            print("\n💡 You can now review the results in the browser.")
            print("📋 The browser will remain open for your review.")
            input("\n⏳ Press Enter when you're ready to close the browser and exit...")

        print("🔄 Closing browser...")
        driver.quit()
        print("✅ Browser closed successfully")

    except Exception as e:
        print(f"⚠️  Error during browser cleanup: {e}")
        print("💡 You may need to close the browser window manually")


def main():
    """
    Main application entry point with comprehensive error handling.
    """
    driver = None

    try:
        # Print welcome message
        print_welcome_message()

        # Initialize browser driver
        print("🔧 Initializing browser...")
        driver = get_browser_driver()

        # Maximize browser window for better visibility
        print("📱 Maximizing browser window...")
        driver.maximize_window()

        print("✅ Browser initialized successfully!")
        print()

        # Run the LinkedIn automation workflow
        success = linkedin_automation_workflow(driver)

        # Print completion message
        print_completion_message(success)

        # Return appropriate exit code
        return 0 if success else 1

    except KeyboardInterrupt:
        print("\n\n⚠️  Application interrupted by user (Ctrl+C)")
        print("🔄 Cleaning up...")
        return 130  # Standard exit code for Ctrl+C

    except Exception as e:
        print(f"\n❌ CRITICAL ERROR: {e}")
        print("💡 Please check your internet connection and try again")
        print("💡 Make sure Chrome or Firefox is properly installed")
        return 1

    finally:
        # Always attempt to clean up the browser
        if driver:
            try:
                handle_browser_cleanup(driver)
            except Exception as cleanup_error:
                print(f"⚠️  Cleanup error: {cleanup_error}")


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)
