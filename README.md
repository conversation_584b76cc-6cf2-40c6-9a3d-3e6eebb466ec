# LinkedIn Auto-Apply Bot 🚀

An intelligent LinkedIn job search automation tool with comprehensive error handling and browser fallback support.

## Features ✨

- **🔍 Smart Browser Detection**: Automatically detects and uses Chrome, falls back to Firefox if Chrome is unavailable
- **🔐 Intelligent Login Detection**: Automatically detects if you're already logged into LinkedIn
- **📋 Multiple Job Queries**: Searches through multiple predefined job queries automatically
- **⚡ Robust Error Handling**: Comprehensive error handling for all failure scenarios
- **📊 Detailed Progress Reporting**: Real-time progress updates and final results summary
- **🛡️ Safe Operation**: Respects LinkedIn's interface and includes human-like delays

## Requirements 📋

### System Requirements
- Python 3.8 or higher
- One of the following browsers:
  - Google Chrome (recommended)
  - Mozilla Firefox (fallback)

### Python Dependencies
All dependencies are listed in `requirements.txt` and will be installed automatically.

## Installation 🛠️

1. **Clone or download the project**
   ```bash
   cd linkedin-autoapply
   ```

2. **Set up virtual environment (recommended)**
   ```bash
   python3 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Test the installation**
   ```bash
   python test_app.py
   ```

## Usage 🚀

### Basic Usage
```bash
# Activate virtual environment (if using one)
source venv/bin/activate

# Run the application
python main.py
```

### What Happens When You Run It

1. **Browser Detection**: The app automatically detects available browsers
2. **Browser Launch**: Opens Chrome (or Firefox as fallback) and maximizes the window
3. **LinkedIn Navigation**: Navigates to LinkedIn homepage
4. **Login Check**: Checks if you're already logged in
5. **Manual Login** (if needed): Prompts you to log in manually if not already logged in
6. **Jobs Navigation**: Automatically navigates to LinkedIn Jobs section
7. **Job Searches**: Performs searches for all configured job queries
8. **Results Summary**: Shows detailed results of all searches

## Configuration ⚙️

### Job Search Queries
Edit `config.py` to customize your job search queries:

```python
JOB_SEARCH_QUERIES = [
    "Web Developer Fresher",
    "Frontend Developer Entry Level",
    "Backend Developer Junior",
    # Add your own queries here
]
```

### Timing Settings
Adjust timing settings in `config.py`:

```python
TIMING_CONFIG = {
    "page_load_wait": 3,        # Time to wait for pages to load
    "element_wait": 5,          # Time to wait for elements to appear
    "search_wait": 5,           # Time to wait after performing search
    "login_check_wait": 3,      # Time to wait when checking login status
    "between_queries_wait": 2,  # Time to wait between different search queries
}
```

## Error Handling 🛡️

The application includes comprehensive error handling for:

- **No browsers available**: Shows clear error message with installation links
- **Browser launch failures**: Automatic fallback from Chrome to Firefox
- **Network connectivity issues**: Graceful handling with user-friendly messages
- **LinkedIn interface changes**: Multiple fallback selectors for elements
- **Login timeouts**: 5-minute timeout with progress updates
- **Element not found**: Retry logic with multiple selector attempts

## File Structure 📁

```
linkedin-autoapply/
├── main.py              # Main application entry point
├── utils.py             # Browser detection and driver utilities
├── linkedin_bot.py      # LinkedIn automation logic
├── config.py            # Configuration settings
├── requirements.txt     # Python dependencies
├── test_app.py         # Test suite
├── README.md           # This file
└── venv/               # Virtual environment (if created)
```

## Troubleshooting 🔧

### Common Issues

1. **"No supported browsers found"**
   - Install Google Chrome: https://www.google.com/chrome/
   - Or install Firefox: https://www.mozilla.org/firefox/

2. **"No module named 'selenium'"**
   ```bash
   pip install -r requirements.txt
   ```

3. **Browser fails to start**
   - Make sure your browser is up to date
   - Try running the test script: `python test_app.py`

4. **Login issues**
   - The app will prompt you to log in manually
   - You have 5 minutes to complete the login process
   - Make sure you're using the correct LinkedIn credentials

### Running Tests
```bash
python test_app.py
```

This will test:
- All imports and dependencies
- Configuration loading
- Browser detection
- Selenium functionality

## Important Notes ⚠️

- **Manual Login Required**: You'll need to log in to LinkedIn manually when prompted
- **Respect LinkedIn's Terms**: This tool is for personal job searching only
- **Rate Limiting**: The app includes delays to avoid overwhelming LinkedIn's servers
- **Browser Window**: Don't close the browser window manually - let the app handle it

## Support 💬

If you encounter any issues:

1. Run the test suite: `python test_app.py`
2. Check that your browser is properly installed
3. Ensure you have a stable internet connection
4. Make sure you're using Python 3.8 or higher

## License 📄

This project is for educational and personal use only. Please respect LinkedIn's Terms of Service.
