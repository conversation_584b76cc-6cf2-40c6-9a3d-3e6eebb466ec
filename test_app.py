#!/usr/bin/env python3
# test_app.py - Test script for LinkedIn Auto-Apply Bot

"""
Test script to verify the LinkedIn Auto-Apply Bot functionality.
This script tests various components without actually running the full automation.
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all required modules can be imported."""
    print("🧪 Testing imports...")
    
    try:
        import config
        print("✅ config.py imported successfully")
        
        from utils import get_browser_driver, check_browser_installed
        print("✅ utils.py imported successfully")
        
        from linkedin_bot import linkedin_automation_workflow
        print("✅ linkedin_bot.py imported successfully")
        
        from main import main
        print("✅ main.py imported successfully")
        
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False


def test_config():
    """Test configuration values."""
    print("\n🧪 Testing configuration...")
    
    try:
        from config import JO<PERSON>_SEARCH_QUERIES, TIMING_CONFIG, LINKEDIN_URLS
        
        print(f"✅ Found {len(JOB_SEARCH_QUERIES)} job search queries")
        print(f"✅ Timing configuration loaded: {len(TIMING_CONFIG)} settings")
        print(f"✅ LinkedIn URLs loaded: {len(LINKEDIN_URLS)} URLs")
        
        # Test that queries are not empty
        if not JOB_SEARCH_QUERIES:
            print("❌ No job search queries found")
            return False
            
        print(f"📋 Sample queries: {JOB_SEARCH_QUERIES[:3]}")
        return True
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False


def test_browser_detection():
    """Test browser detection without actually launching browsers."""
    print("\n🧪 Testing browser detection...")
    
    try:
        from utils import check_browser_installed
        
        chrome_available = check_browser_installed('chrome')
        firefox_available = check_browser_installed('firefox')
        
        print(f"Chrome available: {'✅' if chrome_available else '❌'}")
        print(f"Firefox available: {'✅' if firefox_available else '❌'}")
        
        if not chrome_available and not firefox_available:
            print("⚠️  No browsers detected - the app will show an error when run")
        else:
            print("✅ At least one browser is available")
            
        return True
        
    except Exception as e:
        print(f"❌ Browser detection error: {e}")
        return False


def test_selenium_dependencies():
    """Test that Selenium and webdriver-manager are working."""
    print("\n🧪 Testing Selenium dependencies...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.common.by import By
        from webdriver_manager.chrome import ChromeDriverManager
        from webdriver_manager.firefox import GeckoDriverManager
        
        print("✅ Selenium imported successfully")
        print("✅ WebDriver Manager imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Selenium dependency error: {e}")
        print("💡 Try running: pip install -r requirements.txt")
        return False


def run_all_tests():
    """Run all tests and return overall result."""
    print("🚀 LINKEDIN AUTO-APPLY BOT - TEST SUITE")
    print("="*50)
    
    tests = [
        ("Import Test", test_imports),
        ("Configuration Test", test_config),
        ("Browser Detection Test", test_browser_detection),
        ("Selenium Dependencies Test", test_selenium_dependencies)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
    
    print("\n" + "="*50)
    print(f"📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The application should work correctly.")
        print("💡 Run 'python main.py' to start the LinkedIn automation.")
    else:
        print("⚠️  Some tests failed. Please fix the issues before running the app.")
        
    print("="*50)
    
    return passed == total


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
