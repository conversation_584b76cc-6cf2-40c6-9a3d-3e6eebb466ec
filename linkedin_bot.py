# linkedin_bot.py

from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
import time
from config import JOB_SEARCH_QUERIES


def login_and_search(driver):
    driver.get("https://www.linkedin.com")

    time.sleep(3)

    # Skip login if already logged in
    if "feed" not in driver.current_url:
        print("Please log in manually...")
        input("After login, press Enter to continue...")

    print("Opening job section...")
    driver.get("https://www.linkedin.com/jobs/")

    time.sleep(3)

    # Search for the first query
    search_box = driver.find_element(By.CSS_SELECTOR, "input.jobs-search-box__text-input")
    search_box.clear()
    search_box.send_keys(JOB_SEARCH_QUERIES[0])  # Using first query
    search_box.send_keys(Keys.RETURN)

    print(f"Searching jobs for: {JOB_SEARCH_QUERIES[0]}")
    time.sleep(5)
