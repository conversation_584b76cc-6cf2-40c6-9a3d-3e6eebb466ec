# linkedin_bot.py - LinkedIn automation bot with comprehensive error handling

from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import (
    TimeoutException,
    NoSuchElementException,
    ElementClickInterceptedException,
    StaleElementReferenceException
)
import time
import random
from config import (
    JOB_SEARCH_QUERIES,
    TIMING_CONFIG,
    LINKEDIN_URLS,
    LINKEDIN_SELECTORS,
    APP_CONFIG
)


def wait_for_element(driver, selector, timeout=None, by=By.CSS_SELECTOR):
    """
    Wait for an element to be present and return it.

    Args:
        driver: WebDriver instance
        selector (str): CSS selector or other locator
        timeout (int): Maximum time to wait (uses config default if None)
        by: Selenium By locator type

    Returns:
        WebElement: Found element

    Raises:
        TimeoutException: If element not found within timeout
    """
    if timeout is None:
        timeout = TIMING_CONFIG["element_wait"]

    try:
        wait = WebDriverWait(driver, timeout)
        element = wait.until(EC.presence_of_element_located((by, selector)))
        return element
    except TimeoutException:
        print(f"⚠️  Timeout waiting for element: {selector}")
        raise


def safe_click(driver, element, max_retries=3):
    """
    Safely click an element with retry logic.

    Args:
        driver: WebDriver instance
        element: WebElement to click
        max_retries (int): Maximum number of retry attempts

    Returns:
        bool: True if click successful, False otherwise
    """
    for attempt in range(max_retries):
        try:
            # Scroll element into view
            driver.execute_script("arguments[0].scrollIntoView(true);", element)
            time.sleep(0.5)

            # Try to click
            element.click()
            return True

        except (ElementClickInterceptedException, StaleElementReferenceException) as e:
            print(f"⚠️  Click attempt {attempt + 1} failed: {e}")
            if attempt < max_retries - 1:
                time.sleep(1)
                continue
            else:
                print(f"❌ Failed to click element after {max_retries} attempts")
                return False

    return False


def check_login_status(driver):
    """
    Check if user is already logged into LinkedIn.

    Args:
        driver: WebDriver instance

    Returns:
        bool: True if logged in, False otherwise
    """
    try:
        print("🔍 Checking login status...")

        # Check current URL for indicators of being logged in
        current_url = driver.current_url.lower()

        # If we're on feed, we're definitely logged in
        if "feed" in current_url:
            print("✅ Already logged in (on feed page)")
            return True

        # Look for profile menu or other logged-in indicators
        try:
            wait_for_element(driver, LINKEDIN_SELECTORS["profile_menu"], timeout=3)
            print("✅ Already logged in (profile menu found)")
            return True
        except TimeoutException:
            pass

        # Check if we're on login page
        if "login" in current_url:
            print("❌ Not logged in (on login page)")
            return False

        # Try to find login-specific elements
        try:
            wait_for_element(driver, LINKEDIN_SELECTORS["login_email"], timeout=2)
            print("❌ Not logged in (login form found)")
            return False
        except TimeoutException:
            pass

        print("⚠️  Login status unclear, assuming not logged in")
        return False

    except Exception as e:
        print(f"⚠️  Error checking login status: {e}")
        return False


def navigate_to_linkedin(driver):
    """
    Navigate to LinkedIn homepage and handle initial loading.

    Args:
        driver: WebDriver instance

    Returns:
        bool: True if navigation successful, False otherwise
    """
    try:
        print("🌐 Navigating to LinkedIn...")
        driver.get(LINKEDIN_URLS["home"])

        # Wait for page to load
        time.sleep(TIMING_CONFIG["page_load_wait"])

        print("✅ Successfully loaded LinkedIn homepage")
        return True

    except Exception as e:
        print(f"❌ Failed to navigate to LinkedIn: {e}")
        return False


def handle_login_process(driver):
    """
    Handle the LinkedIn login process.

    Args:
        driver: WebDriver instance

    Returns:
        bool: True if login successful or already logged in, False otherwise
    """
    try:
        # Check if already logged in
        if check_login_status(driver):
            return True

        print("🔐 Login required...")
        print("Please log in manually in the browser window.")
        print("After logging in, the automation will continue automatically.")

        # Wait for user to log in manually
        login_timeout = 300  # 5 minutes timeout for manual login
        start_time = time.time()

        while time.time() - start_time < login_timeout:
            time.sleep(TIMING_CONFIG["login_check_wait"])

            if check_login_status(driver):
                print("✅ Login successful!")
                return True

            # Show progress
            elapsed = int(time.time() - start_time)
            remaining = login_timeout - elapsed
            print(f"⏳ Waiting for login... ({remaining}s remaining)")

        print("❌ Login timeout reached")
        return False

    except Exception as e:
        print(f"❌ Error during login process: {e}")
        return False


def navigate_to_jobs_section(driver):
    """
    Navigate to LinkedIn Jobs section.

    Args:
        driver: WebDriver instance

    Returns:
        bool: True if navigation successful, False otherwise
    """
    try:
        print("💼 Opening LinkedIn Jobs section...")
        driver.get(LINKEDIN_URLS["jobs"])

        # Wait for jobs page to load
        time.sleep(TIMING_CONFIG["page_load_wait"])

        # Verify we're on the jobs page
        if "jobs" in driver.current_url.lower():
            print("✅ Successfully opened Jobs section")
            return True
        else:
            print("⚠️  May not be on jobs page, but continuing...")
            return True

    except Exception as e:
        print(f"❌ Failed to navigate to Jobs section: {e}")
        return False


def search_for_job(driver, query):
    """
    Search for a specific job query on LinkedIn Jobs.

    Args:
        driver: WebDriver instance
        query (str): Job search query

    Returns:
        bool: True if search successful, False otherwise
    """
    try:
        print(f"🔍 Searching for: '{query}'")

        # Find the search box with multiple fallback selectors
        search_selectors = [
            LINKEDIN_SELECTORS["job_search_box"],
            "input[placeholder*='Search jobs']",
            "input.jobs-search-box__text-input",
            "input[aria-label*='Search jobs']"
        ]

        search_box = None
        for selector in search_selectors:
            try:
                search_box = wait_for_element(driver, selector, timeout=5)
                break
            except TimeoutException:
                continue

        if not search_box:
            print("❌ Could not find job search box")
            return False

        # Clear and enter search query
        search_box.clear()
        time.sleep(0.5)

        # Type query with human-like delays
        for char in query:
            search_box.send_keys(char)
            time.sleep(random.uniform(0.05, 0.15))

        # Submit search
        search_box.send_keys(Keys.RETURN)

        # Wait for search results to load
        time.sleep(TIMING_CONFIG["search_wait"])

        print(f"✅ Search completed for: '{query}'")
        return True

    except Exception as e:
        print(f"❌ Error searching for '{query}': {e}")
        return False


def process_all_job_queries(driver):
    """
    Process all job search queries from the configuration.

    Args:
        driver: WebDriver instance

    Returns:
        dict: Results summary with success/failure counts
    """
    results = {
        "total_queries": len(JOB_SEARCH_QUERIES),
        "successful_searches": 0,
        "failed_searches": 0,
        "processed_queries": []
    }

    print(f"📋 Processing {results['total_queries']} job search queries...")

    for i, query in enumerate(JOB_SEARCH_QUERIES, 1):
        print(f"\n--- Query {i}/{results['total_queries']} ---")

        try:
            # Perform search
            if search_for_job(driver, query):
                results["successful_searches"] += 1
                results["processed_queries"].append({"query": query, "status": "success"})

                # Add some analysis time for each search
                print(f"⏳ Analyzing results for '{query}'...")
                time.sleep(TIMING_CONFIG["between_queries_wait"])

            else:
                results["failed_searches"] += 1
                results["processed_queries"].append({"query": query, "status": "failed"})

        except Exception as e:
            print(f"❌ Unexpected error processing '{query}': {e}")
            results["failed_searches"] += 1
            results["processed_queries"].append({"query": query, "status": "error", "error": str(e)})

        # Wait between queries to avoid being too aggressive
        if i < results["total_queries"]:
            print(f"⏳ Waiting before next query...")
            time.sleep(TIMING_CONFIG["between_queries_wait"])

    return results


def print_results_summary(results):
    """
    Print a summary of the job search results.

    Args:
        results (dict): Results dictionary from process_all_job_queries
    """
    print("\n" + "="*60)
    print("📊 JOB SEARCH RESULTS SUMMARY")
    print("="*60)
    print(f"Total Queries: {results['total_queries']}")
    print(f"Successful Searches: {results['successful_searches']}")
    print(f"Failed Searches: {results['failed_searches']}")
    print(f"Success Rate: {(results['successful_searches']/results['total_queries']*100):.1f}%")

    print("\n📝 Detailed Results:")
    for i, result in enumerate(results["processed_queries"], 1):
        status_emoji = "✅" if result["status"] == "success" else "❌"
        print(f"{i:2d}. {status_emoji} {result['query']}")
        if result["status"] == "error":
            print(f"     Error: {result.get('error', 'Unknown error')}")

    print("="*60)


def linkedin_automation_workflow(driver):
    """
    Main LinkedIn automation workflow.

    Args:
        driver: WebDriver instance

    Returns:
        bool: True if workflow completed successfully, False otherwise
    """
    try:
        print("🚀 Starting LinkedIn Auto-Apply Workflow")
        print("="*50)

        # Step 1: Navigate to LinkedIn
        if not navigate_to_linkedin(driver):
            print("❌ Failed to navigate to LinkedIn")
            return False

        # Step 2: Handle login process
        if not handle_login_process(driver):
            print("❌ Login process failed")
            return False

        # Step 3: Navigate to Jobs section
        if not navigate_to_jobs_section(driver):
            print("❌ Failed to navigate to Jobs section")
            return False

        # Step 4: Process all job search queries
        results = process_all_job_queries(driver)

        # Step 5: Print results summary
        print_results_summary(results)

        # Determine overall success
        if results["successful_searches"] > 0:
            print("✅ Workflow completed with some successful searches")
            return True
        else:
            print("❌ Workflow completed but no searches were successful")
            return False

    except Exception as e:
        print(f"❌ Critical error in workflow: {e}")
        return False


# Legacy function for backward compatibility
def login_and_search(driver):
    """
    Legacy function that calls the new workflow.
    Maintained for backward compatibility.

    Args:
        driver: WebDriver instance
    """
    return linkedin_automation_workflow(driver)
